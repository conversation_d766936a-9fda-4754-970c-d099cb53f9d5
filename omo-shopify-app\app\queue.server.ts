import { Queue } from "bullmq";
import type { RedisOptions } from "ioredis";

const connection: RedisOptions = {
  host: "localhost",
  port: Number(process.env.REDIS_PORT || 6379),
};

const g = global as any;
g.__queues ??= {};

export function getQueue(name: string) {
  if (!g.__queues[name]) {
    // creating a Queue does NOT start processing; it's safe for producers only
    g.__queues[name] = new Queue(name, { connection });
  }
  return g.__queues[name] as Queue;
}
